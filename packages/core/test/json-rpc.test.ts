import { describe, it, expect } from 'vitest'
import {
  jsonrpcRequest,
  jsonrpcNotification,
  jsonrpcSuccessRespone,
  jsonrpcErrorResponse,
  isJsonrpcRequest,
  isJsonrpcNotification,
  isJsonrpcResponse,
  isJsonrpcSuccessResponse,
  isJsonrpcErrorResponse,
  parseJsonrpcMessage,
  type JsonrpcRequest,
  type JsonrpcNotification,
  type JsonrpcSuccessResponse,
  type JsonrpcErrorResponse,
  type JsonrpcMessage,
} from '../src/utils/json-rpc'

describe('json-rpc', () => {
  describe('jsonrpcRequest', () => {
    it('应该创建一个有效的JSON-RPC请求对象', () => {
      const request = jsonrpcRequest({
        method: 'test.method',
        id: 'test-id-123',
        params: { foo: 'bar' },
      })

      expect(request).toEqual({
        jsonrpc: '2.0',
        method: 'test.method',
        id: 'test-id-123',
        params: { foo: 'bar' },
      })
    })

    it('应该创建没有params的请求对象', () => {
      const request = jsonrpcRequest({
        method: 'test.method',
        id: 'test-id-123',
      })

      expect(request).toEqual({
        jsonrpc: '2.0',
        method: 'test.method',
        id: 'test-id-123',
      })
    })

    it('应该正确处理复杂的params对象', () => {
      const complexParams = {
        array: [1, 2, 3],
        nested: { deep: { value: 'test' } },
        null: null,
        boolean: true,
        number: 42,
      }

      const request = jsonrpcRequest({
        method: 'complex.method',
        id: 'complex-id',
        params: complexParams,
      })

      expect(request.params).toEqual(complexParams)
      expect(request.jsonrpc).toBe('2.0')
    })
  })

  describe('jsonrpcNotification', () => {
    it('应该创建一个有效的JSON-RPC通知对象（没有id）', () => {
      const notification = jsonrpcNotification({
        method: 'notification.method',
        params: { message: 'hello' },
      })

      expect(notification).toEqual({
        jsonrpc: '2.0',
        method: 'notification.method',
        params: { message: 'hello' },
      })
      expect(notification).not.toHaveProperty('id')
    })

    it('应该创建没有params的通知对象', () => {
      const notification = jsonrpcNotification({
        method: 'simple.notification',
      })

      expect(notification).toEqual({
        jsonrpc: '2.0',
        method: 'simple.notification',
      })
      expect(notification).not.toHaveProperty('id')
    })

    it('应该正确处理数组类型的params', () => {
      const arrayParams = [1, 'string', { key: 'value' }, null]

      const notification = jsonrpcNotification({
        method: 'array.notification',
        params: arrayParams,
      })

      expect(notification.params).toEqual(arrayParams)
      expect(notification).not.toHaveProperty('id')
    })
  })

  describe('jsonrpcSuccessRespone', () => {
    it('应该创建一个有效的JSON-RPC成功响应对象', () => {
      const response = jsonrpcSuccessRespone({
        id: 'test-id',
        result: { success: true, data: 'test-data' },
      })

      expect(response).toEqual({
        jsonrpc: '2.0',
        id: 'test-id',
        result: { success: true, data: 'test-data' },
      })
    })

    it('应该正确处理不同类型的result', () => {
      const testCases = [
        { result: 'string result' },
        { result: 123 },
        { result: true },
        { result: null },
        { result: [1, 2, 3] },
        { result: { complex: { nested: 'object' } } },
      ]

      testCases.forEach(({ result }, index) => {
        const response = jsonrpcSuccessRespone({
          id: `test-${index}`,
          result,
        })

        expect(response.result).toEqual(result)
        expect(response.jsonrpc).toBe('2.0')
        expect(response.id).toBe(`test-${index}`)
      })
    })
  })

  describe('jsonrpcErrorResponse', () => {
    it('应该创建一个有效的JSON-RPC错误响应对象', () => {
      const response = jsonrpcErrorResponse({
        id: 'test-id',
        error: {
          code: -32601,
          message: 'Method not found',
          data: { additional: 'info' },
        },
      })

      expect(response).toEqual({
        jsonrpc: '2.0',
        id: 'test-id',
        error: {
          code: -32601,
          message: 'Method not found',
          data: { additional: 'info' },
        },
      })
    })

    it('应该创建没有data的错误响应', () => {
      const response = jsonrpcErrorResponse({
        id: 'test-id',
        error: {
          code: -32600,
          message: 'Invalid Request',
        },
      })

      expect(response).toEqual({
        jsonrpc: '2.0',
        id: 'test-id',
        error: {
          code: -32600,
          message: 'Invalid Request',
        },
      })
    })
  })

  describe('类型检查函数', () => {
    describe('isJsonrpcRequest', () => {
      it('应该正确识别JSON-RPC请求', () => {
        const request: JsonrpcRequest = {
          jsonrpc: '2.0',
          id: 'test-id',
          method: 'test.method',
          params: { foo: 'bar' },
        }

        expect(isJsonrpcRequest(request)).toBe(true)
      })

      it('应该正确识别没有params的请求', () => {
        const request: JsonrpcRequest = {
          jsonrpc: '2.0',
          id: 'test-id',
          method: 'test.method',
        }

        expect(isJsonrpcRequest(request)).toBe(true)
      })

      it('应该拒绝通知对象（没有id）', () => {
        const notification: JsonrpcNotification = {
          jsonrpc: '2.0',
          method: 'test.method',
        }

        expect(isJsonrpcRequest(notification)).toBe(false)
      })

      it('应该拒绝响应对象', () => {
        const response: JsonrpcSuccessResponse = {
          jsonrpc: '2.0',
          id: 'test-id',
          result: 'success',
        }

        expect(isJsonrpcRequest(response)).toBe(false)
      })
    })

    describe('isJsonrpcNotification', () => {
      it('应该正确识别JSON-RPC通知', () => {
        const notification: JsonrpcNotification = {
          jsonrpc: '2.0',
          method: 'test.method',
          params: { foo: 'bar' },
        }

        expect(isJsonrpcNotification(notification)).toBe(true)
      })

      it('应该正确识别没有params的通知', () => {
        const notification: JsonrpcNotification = {
          jsonrpc: '2.0',
          method: 'test.method',
        }

        expect(isJsonrpcNotification(notification)).toBe(true)
      })

      it('应该拒绝请求对象（有id）', () => {
        const request: JsonrpcRequest = {
          jsonrpc: '2.0',
          id: 'test-id',
          method: 'test.method',
        }

        expect(isJsonrpcNotification(request)).toBe(false)
      })

      it('应该拒绝响应对象', () => {
        const response: JsonrpcSuccessResponse = {
          jsonrpc: '2.0',
          id: 'test-id',
          result: 'success',
        }

        expect(isJsonrpcNotification(response)).toBe(false)
      })
    })

    describe('isJsonrpcResponse', () => {
      it('应该正确识别成功响应', () => {
        const response: JsonrpcSuccessResponse = {
          jsonrpc: '2.0',
          id: 'test-id',
          result: 'success',
        }

        expect(isJsonrpcResponse(response)).toBe(true)
      })

      it('应该正确识别错误响应', () => {
        const response: JsonrpcErrorResponse = {
          jsonrpc: '2.0',
          id: 'test-id',
          error: {
            code: -32601,
            message: 'Method not found',
          },
        }

        expect(isJsonrpcResponse(response)).toBe(true)
      })

      it('应该拒绝请求对象', () => {
        const request: JsonrpcRequest = {
          jsonrpc: '2.0',
          id: 'test-id',
          method: 'test.method',
        }

        expect(isJsonrpcResponse(request)).toBe(false)
      })

      it('应该拒绝通知对象', () => {
        const notification: JsonrpcNotification = {
          jsonrpc: '2.0',
          method: 'test.method',
        }

        expect(isJsonrpcResponse(notification)).toBe(false)
      })
    })

    describe('isJsonrpcSuccessResponse', () => {
      it('应该正确识别成功响应', () => {
        const response: JsonrpcSuccessResponse = {
          jsonrpc: '2.0',
          id: 'test-id',
          result: 'success',
        }

        expect(isJsonrpcSuccessResponse(response)).toBe(true)
      })

      it('应该拒绝错误响应', () => {
        const response: JsonrpcErrorResponse = {
          jsonrpc: '2.0',
          id: 'test-id',
          error: {
            code: -32601,
            message: 'Method not found',
          },
        }

        expect(isJsonrpcSuccessResponse(response)).toBe(false)
      })

      it('应该拒绝请求对象', () => {
        const request: JsonrpcRequest = {
          jsonrpc: '2.0',
          id: 'test-id',
          method: 'test.method',
        }

        expect(isJsonrpcSuccessResponse(request)).toBe(false)
      })
    })

    describe('isJsonrpcErrorResponse', () => {
      it('应该正确识别错误响应', () => {
        const response: JsonrpcErrorResponse = {
          jsonrpc: '2.0',
          id: 'test-id',
          error: {
            code: -32601,
            message: 'Method not found',
          },
        }

        expect(isJsonrpcErrorResponse(response)).toBe(true)
      })

      it('应该拒绝成功响应', () => {
        const response: JsonrpcSuccessResponse = {
          jsonrpc: '2.0',
          id: 'test-id',
          result: 'success',
        }

        expect(isJsonrpcErrorResponse(response)).toBe(false)
      })

      it('应该拒绝请求对象', () => {
        const request: JsonrpcRequest = {
          jsonrpc: '2.0',
          id: 'test-id',
          method: 'test.method',
        }

        expect(isJsonrpcErrorResponse(request)).toBe(false)
      })
    })
  })

  describe('parseJsonrpcMessage', () => {
    it('应该解析有效的JSON-RPC请求', () => {
      const requestData = {
        jsonrpc: '2.0',
        id: 'test-id',
        method: 'test.method',
        params: { foo: 'bar' },
      }

      const parsed = parseJsonrpcMessage(requestData)

      expect(parsed).toEqual(requestData)
      expect(isJsonrpcRequest(parsed)).toBe(true)
    })

    it('应该解析有效的JSON-RPC通知', () => {
      const notificationData = {
        jsonrpc: '2.0',
        method: 'test.notification',
        params: { message: 'hello' },
      }

      const parsed = parseJsonrpcMessage(notificationData)

      expect(parsed).toEqual(notificationData)
      expect(isJsonrpcNotification(parsed)).toBe(true)
    })

    it('应该解析有效的成功响应', () => {
      const responseData = {
        jsonrpc: '2.0',
        id: 'test-id',
        result: { success: true, data: 'test-data' },
      }

      const parsed = parseJsonrpcMessage(responseData)

      expect(parsed).toEqual(responseData)
      expect(isJsonrpcSuccessResponse(parsed)).toBe(true)
    })

    it('应该解析有效的错误响应', () => {
      const responseData = {
        jsonrpc: '2.0',
        id: 'test-id',
        error: {
          code: -32601,
          message: 'Method not found',
          data: { additional: 'info' },
        },
      }

      const parsed = parseJsonrpcMessage(responseData)

      expect(parsed).toEqual(responseData)
      expect(isJsonrpcErrorResponse(parsed)).toBe(true)
    })

    it('应该解析JSON字符串', () => {
      const requestData = {
        jsonrpc: '2.0',
        id: 'test-id',
        method: 'test.method',
      }
      const jsonString = JSON.stringify(requestData)

      const parsed = parseJsonrpcMessage(jsonString)

      expect(parsed).toEqual(requestData)
    })

    it('应该在消息无效时抛出错误', () => {
      const invalidCases = [null, undefined, 'invalid json', 123, [], 'not json', '{"invalid": json}']

      invalidCases.forEach(invalidCase => {
        expect(() => parseJsonrpcMessage(invalidCase)).toThrow('Invalid json-rpc message')
      })
    })

    it('应该在jsonrpc版本不正确时抛出错误', () => {
      const invalidVersions = [
        { jsonrpc: '1.0', id: 'test', method: 'test' },
        { jsonrpc: '3.0', id: 'test', method: 'test' },
        { jsonrpc: 2.0, id: 'test', method: 'test' },
        { id: 'test', method: 'test' }, // 缺少jsonrpc字段
      ]

      invalidVersions.forEach(invalidVersion => {
        expect(() => parseJsonrpcMessage(invalidVersion)).toThrow('Invalid json-rpc message')
      })
    })

    it('应该在消息格式不正确时抛出错误', () => {
      const invalidMessages = [
        { jsonrpc: '2.0' }, // 缺少必要字段
        { jsonrpc: '2.0', id: 'test' }, // 缺少method或result/error
        // 注意：{ jsonrpc: '2.0', method: 'test', result: 'test' } 实际上会被识别为通知，因为没有id
        // 注意：同时有result和error的情况，isJsonrpcResponse会返回true，因为它只检查是否有result或error
      ]

      invalidMessages.forEach(invalidMessage => {
        expect(() => parseJsonrpcMessage(invalidMessage)).toThrow('Invalid json-rpc message')
      })
    })

    it('应该正确处理复杂的嵌套数据', () => {
      const complexData = {
        jsonrpc: '2.0',
        id: 'complex-test',
        result: {
          users: [
            { id: 1, name: 'Alice', metadata: { active: true } },
            { id: 2, name: 'Bob', metadata: { active: false } },
          ],
          pagination: {
            total: 2,
            page: 1,
            limit: 10,
          },
          timestamp: '2023-01-01T00:00:00Z',
        },
      }

      const parsed = parseJsonrpcMessage(complexData)

      expect(parsed).toEqual(complexData)
      if (isJsonrpcSuccessResponse(parsed)) {
        expect(parsed.result).toEqual(complexData.result)
      }
    })

    it('应该正确处理不同类型的参数', () => {
      const testCases = [
        { params: 'string params' },
        { params: 123 },
        { params: true },
        { params: null },
        { params: [1, 2, 3] },
        { params: { nested: { deep: 'object' } } },
      ]

      testCases.forEach(({ params }, index) => {
        const requestData = {
          jsonrpc: '2.0',
          id: `test-${index}`,
          method: 'test.method',
          params,
        }

        const parsed = parseJsonrpcMessage(requestData)
        if (isJsonrpcRequest(parsed)) {
          expect(parsed.params).toEqual(params)
        }
      })
    })

    it('应该正确处理边界情况', () => {
      // 测试空字符串method
      const emptyMethodRequest = {
        jsonrpc: '2.0',
        id: 'test',
        method: '',
      }
      const parsed1 = parseJsonrpcMessage(emptyMethodRequest)
      expect(isJsonrpcRequest(parsed1)).toBe(true)

      // 测试数字id
      const numericIdRequest = {
        jsonrpc: '2.0',
        id: 123,
        method: 'test.method',
      }
      const parsed2 = parseJsonrpcMessage(numericIdRequest)
      expect(isJsonrpcRequest(parsed2)).toBe(true)

      // 测试null result
      const nullResultResponse = {
        jsonrpc: '2.0',
        id: 'test',
        result: null,
      }
      const parsed3 = parseJsonrpcMessage(nullResultResponse)
      expect(isJsonrpcSuccessResponse(parsed3)).toBe(true)

      // 测试零错误码
      const zeroErrorResponse = {
        jsonrpc: '2.0',
        id: 'test',
        error: {
          code: 0,
          message: 'Success but error format',
        },
      }
      const parsed4 = parseJsonrpcMessage(zeroErrorResponse)
      expect(isJsonrpcErrorResponse(parsed4)).toBe(true)
    })

    it('应该正确处理特殊字符', () => {
      // 测试包含特殊字符的method
      const specialCharMethod = {
        jsonrpc: '2.0',
        method: 'test.method-with_special.chars/and\\backslash',
        params: { emoji: '🚀', unicode: 'café' },
      }
      const parsed = parseJsonrpcMessage(specialCharMethod)
      expect(isJsonrpcNotification(parsed)).toBe(true)
      if (isJsonrpcNotification(parsed)) {
        expect(parsed.method).toBe('test.method-with_special.chars/and\\backslash')
        expect(parsed.params).toEqual({ emoji: '🚀', unicode: 'café' })
      }
    })
  })

  describe('边界情况和错误处理', () => {
    it('jsonrpcRequest 应该正确处理 undefined params', () => {
      const request = jsonrpcRequest({
        method: 'test.method',
        id: 'test-id',
        params: undefined,
      })

      expect(request.params).toBeUndefined()
      expect(request.jsonrpc).toBe('2.0')
    })

    it('jsonrpcNotification 应该正确处理 undefined params', () => {
      const notification = jsonrpcNotification({
        method: 'test.method',
        params: undefined,
      })

      expect(notification.params).toBeUndefined()
      expect(notification.jsonrpc).toBe('2.0')
    })

    it('jsonrpcSuccessRespone 应该正确处理 undefined result', () => {
      const response = jsonrpcSuccessRespone({
        id: 'test-id',
        result: undefined,
      })

      expect(response.result).toBeUndefined()
      expect(response.jsonrpc).toBe('2.0')
    })

    it('jsonrpcErrorResponse 应该正确处理没有 data 的错误', () => {
      const response = jsonrpcErrorResponse({
        id: 'test-id',
        error: {
          code: -32600,
          message: 'Invalid Request',
        },
      })

      expect(response.error.data).toBeUndefined()
      expect(response.jsonrpc).toBe('2.0')
    })

    it('类型检查函数应该正确处理空对象', () => {
      const emptyObj = { jsonrpc: '2.0' } as any

      expect(isJsonrpcRequest(emptyObj)).toBe(false)
      expect(isJsonrpcNotification(emptyObj)).toBe(false)
      expect(isJsonrpcResponse(emptyObj)).toBe(false)
      expect(isJsonrpcSuccessResponse(emptyObj)).toBe(false)
      expect(isJsonrpcErrorResponse(emptyObj)).toBe(false)
    })

    it('类型检查函数应该正确处理包含额外属性的对象', () => {
      const requestWithExtra = {
        jsonrpc: '2.0',
        id: 'test',
        method: 'test.method',
        extraProperty: 'should be ignored',
        anotherExtra: 123,
      } as any

      expect(isJsonrpcRequest(requestWithExtra)).toBe(true)
      expect(isJsonrpcNotification(requestWithExtra)).toBe(false)
      expect(isJsonrpcResponse(requestWithExtra)).toBe(false)
    })
  })
})
